import { useState, useEffect, ReactNode } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';
import Image from 'next/image';

// Define variants using class-variance-authority
const flashSaleBannerVariants = cva('flex items-center rounded-card border gap-2 w-full', {
  variants: {
    variant: {
      default: 'border-secondary bg-secondary-bg text-black',
      primary: 'border-blue-400 bg-blue-50 text-black',
      secondary: 'border-purple-400 bg-purple-50 text-black',
      danger: 'border-red-400 bg-red-50 text-black',
    },
    size: {
      sm: 'text-sm min-h-[80px]',
      default: 'text-base sm:h-[96px] sm:w-[548px]', // Fixed size only on sm and above
      lg: 'text-lg min-h-[112px]',
    },
  },
  defaultVariants: {
    variant: 'default',
    size: 'default',
  },
});

// Timer box
const timerBoxVariants = cva('font-bold px-3 py-2 text-xl rounded-sm', {
  variants: {
    variant: {
      default: 'bg-[#FFD904] text-black',
      primary: 'bg-blue-400 text-white',
      secondary: 'bg-purple-400 text-white',
      danger: 'bg-red-400 text-white',
    },
  },
  defaultVariants: {
    variant: 'default',
  },
});

// Discount badge
const discountBadgeVariants = cva('px-2 py-1 rounded-full text-xs font-bold', {
  variants: {
    variant: {
      default: 'bg-red-500 text-white',
      primary: 'bg-blue-600 text-white',
      secondary: 'bg-purple-600 text-white',
      danger: 'bg-red-600 text-white',
    },
  },
  defaultVariants: {
    variant: 'default',
  },
});

// Props
export interface FlashSaleBannerProps extends VariantProps<typeof flashSaleBannerVariants> {
  className?: string;
  endTime: Date | string;
  title?: string;
  subtitle?: string;
  discount?: string;
  showDiscount?: boolean;
  onTimeEnd?: () => void;
  icon?: ReactNode;
  iconPosition?: 'left' | 'right';
  testId?: string;
}

const FlashSaleBanner = ({
  className,
  variant,
  size,
  endTime,
  title = 'FLASH SALE',
  subtitle = 'แสงอรุณนครินทร์',
  discount = '-11%',
  showDiscount = false,
  onTimeEnd,
  icon = (
    <Image
      src="/images/lightning.png"
      alt="Flash sale icon"
      width={52}
      height={52}
      className="h-7 w-7 object-contain sm:h-[52px] sm:w-[52px]"
    />
  ),
  iconPosition = 'right',
  testId = 'flash-sale-banner',
}: FlashSaleBannerProps) => {
  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());

  function calculateTimeLeft() {
    const endDateTime = endTime instanceof Date ? endTime : new Date(endTime);
    const difference = endDateTime.getTime() - new Date().getTime();

    if (difference <= 0) {
      if (onTimeEnd) {
        onTimeEnd();
      }
      return { hours: '00', minutes: '00', seconds: '00' };
    }

    return {
      hours: Math.floor((difference / (1000 * 60 * 60)) % 24)
        .toString()
        .padStart(2, '0'),
      minutes: Math.floor((difference / 1000 / 60) % 60)
        .toString()
        .padStart(2, '0'),
      seconds: Math.floor((difference / 1000) % 60)
        .toString()
        .padStart(2, '0'),
    };
  }

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearInterval(timer);
  }, [endTime]);

  return (
    <div
      className={cn(
        flashSaleBannerVariants({ variant, size, className }),
        'mx-auto flex-col sm:flex-row',
      )}
      data-testid={testId}
      role="alert"
      aria-live="polite"
    >
      <div className="flex h-full w-full flex-col items-center gap-3 px-4 text-center sm:flex-row sm:justify-between sm:px-4 sm:text-left">
        {/* Text & icon */}
        <div className="flex flex-col items-center sm:flex-row sm:items-center">
          {iconPosition === 'left' && <div className="text-current">{icon}</div>}
          <div className="font-bold sm:ml-2 sm:py-[22px]">
            <div className="flex items-center justify-center sm:justify-start">
              <span className="text-text-lg-bold sm:text-text-3xl flex items-center">
                FLASH
                <span className="-mx-1 -mt-1 inline-flex items-start justify-center sm:-mx-2">
                  {icon}
                </span>
                SALE
              </span>
            </div>
            <span className="sm:text-body-16-none text-title block text-sm font-medium">
              {subtitle}
            </span>
          </div>
        </div>

        {/* Timer & discount */}
        <div className="flex flex-col items-center gap-2 sm:flex-row sm:items-center">
          {showDiscount && <div className={cn(discountBadgeVariants({ variant }))}>{discount}</div>}
          <div
            className="flex"
            aria-label={`Time remaining: ${timeLeft.hours} hours, ${timeLeft.minutes} minutes, and ${timeLeft.seconds} seconds`}
          >
            <div className={cn(timerBoxVariants({ variant }))}>{timeLeft.hours}</div>
            <div className="flex items-center px-1 text-xl font-bold text-current">:</div>
            <div className={cn(timerBoxVariants({ variant }))}>{timeLeft.minutes}</div>
            <div className="flex items-center px-1 text-xl font-bold text-current">:</div>
            <div className={cn(timerBoxVariants({ variant }))}>{timeLeft.seconds}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FlashSaleBanner;
