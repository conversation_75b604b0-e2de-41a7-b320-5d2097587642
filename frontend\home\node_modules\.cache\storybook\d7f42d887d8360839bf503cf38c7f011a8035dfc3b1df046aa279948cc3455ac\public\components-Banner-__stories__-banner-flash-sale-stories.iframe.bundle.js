"use strict";
(self["webpackChunkshadcn_timeline"] = self["webpackChunkshadcn_timeline"] || []).push([["components-Banner-__stories__-banner-flash-sale-stories"],{

/***/ "./node_modules/class-variance-authority/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/class-variance-authority/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   cva: () => (/* binding */ cva),
/* harmony export */   cx: () => (/* binding */ cx)
/* harmony export */ });
/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ "./node_modules/clsx/dist/clsx.mjs");
/**
 * Copyright 2022 Joe Bell. All rights reserved.
 *
 * This file is licensed to you under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with the
 * License. You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */ 
const falsyToString = (value)=>typeof value === "boolean" ? `${value}` : value === 0 ? "0" : value;
const cx = clsx__WEBPACK_IMPORTED_MODULE_0__.clsx;
const cva = (base, config)=>(props)=>{
        var _config_compoundVariants;
        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);
        const { variants, defaultVariants } = config;
        const getVariantClassNames = Object.keys(variants).map((variant)=>{
            const variantProp = props === null || props === void 0 ? void 0 : props[variant];
            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];
            if (variantProp === null) return null;
            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);
            return variants[variant][variantKey];
        });
        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{
            let [key, value] = param;
            if (value === undefined) {
                return acc;
            }
            acc[key] = value;
            return acc;
        }, {});
        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{
            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;
            return Object.entries(compoundVariantOptions).every((param)=>{
                let [key, value] = param;
                return Array.isArray(value) ? value.includes({
                    ...defaultVariants,
                    ...propsWithoutUndefined
                }[key]) : ({
                    ...defaultVariants,
                    ...propsWithoutUndefined
                })[key] === value;
            }) ? [
                ...acc,
                cvClass,
                cvClassName
            ] : acc;
        }, []);
        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);
    };



/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/Icon.js":
/*!****************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/Icon.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ Icon)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaultAttributes.js */ "./node_modules/lucide-react/dist/esm/defaultAttributes.js");
/* harmony import */ var _shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/src/utils.js */ "./node_modules/lucide-react/dist/esm/shared/src/utils.js");
/**
 * @license lucide-react v0.483.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */





const Icon = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(
  ({
    color = "currentColor",
    size = 24,
    strokeWidth = 2,
    absoluteStrokeWidth,
    className = "",
    children,
    iconNode,
    ...rest
  }, ref) => {
    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(
      "svg",
      {
        ref,
        ..._defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__["default"],
        width: size,
        height: size,
        stroke: color,
        strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,
        className: (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.mergeClasses)("lucide", className),
        ...rest
      },
      [
        ...iconNode.map(([tag, attrs]) => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(tag, attrs)),
        ...Array.isArray(children) ? children : [children]
      ]
    );
  }
);


//# sourceMappingURL=Icon.js.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/createLucideIcon.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/createLucideIcon.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ createLucideIcon)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/src/utils.js */ "./node_modules/lucide-react/dist/esm/shared/src/utils.js");
/* harmony import */ var _Icon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Icon.js */ "./node_modules/lucide-react/dist/esm/Icon.js");
/**
 * @license lucide-react v0.483.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */





const createLucideIcon = (iconName, iconNode) => {
  const Component = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(
    ({ className, ...props }, ref) => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_Icon_js__WEBPACK_IMPORTED_MODULE_1__["default"], {
      ref,
      iconNode,
      className: (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.mergeClasses)(`lucide-${(0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toKebabCase)(iconName)}`, className),
      ...props
    })
  );
  Component.displayName = `${iconName}`;
  return Component;
};


//# sourceMappingURL=createLucideIcon.js.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/defaultAttributes.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/defaultAttributes.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ defaultAttributes)
/* harmony export */ });
/**
 * @license lucide-react v0.483.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

var defaultAttributes = {
  xmlns: "http://www.w3.org/2000/svg",
  width: 24,
  height: 24,
  viewBox: "0 0 24 24",
  fill: "none",
  stroke: "currentColor",
  strokeWidth: 2,
  strokeLinecap: "round",
  strokeLinejoin: "round"
};


//# sourceMappingURL=defaultAttributes.js.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/icons/clock.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/clock.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   __iconNode: () => (/* binding */ __iconNode),
/* harmony export */   "default": () => (/* binding */ Clock)
/* harmony export */ });
/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ "./node_modules/lucide-react/dist/esm/createLucideIcon.js");
/**
 * @license lucide-react v0.483.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */



const __iconNode = [
  ["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }],
  ["polyline", { points: "12 6 12 12 16 14", key: "68esgv" }]
];
const Clock = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__["default"])("Clock", __iconNode);


//# sourceMappingURL=clock.js.map


/***/ }),

/***/ "./node_modules/lucide-react/dist/esm/shared/src/utils.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/shared/src/utils.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   mergeClasses: () => (/* binding */ mergeClasses),
/* harmony export */   toKebabCase: () => (/* binding */ toKebabCase)
/* harmony export */ });
/**
 * @license lucide-react v0.483.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

const toKebabCase = (string) => string.replace(/([a-z0-9])([A-Z])/g, "$1-$2").toLowerCase();
const mergeClasses = (...classes) => classes.filter((className, index, array) => {
  return Boolean(className) && className.trim() !== "" && array.indexOf(className) === index;
}).join(" ").trim();


//# sourceMappingURL=utils.js.map


/***/ }),

/***/ "./src/components/Banner/__fixtures__/banner-flash-sale.fixtures.tsx":
/*!***************************************************************************!*\
  !*** ./src/components/Banner/__fixtures__/banner-flash-sale.fixtures.tsx ***!
  \***************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

var C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0___namespace_cache;
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   allFixtures: () => (/* binding */ allFixtures),
/* harmony export */   almostEndingFixture: () => (/* binding */ almostEndingFixture),
/* harmony export */   customIconFixture: () => (/* binding */ customIconFixture),
/* harmony export */   defaultFixture: () => (/* binding */ defaultFixture),
/* harmony export */   largeFixture: () => (/* binding */ largeFixture),
/* harmony export */   primaryVariantFixture: () => (/* binding */ primaryVariantFixture),
/* harmony export */   secondaryVariantFixture: () => (/* binding */ secondaryVariantFixture),
/* harmony export */   withDiscount: () => (/* binding */ withDiscount)
/* harmony export */ });
/* harmony import */ var C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-refresh/runtime.js */ "./node_modules/react-refresh/runtime.js");
/* harmony import */ var _banner_flash_sale__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../banner-flash-sale */ "./src/components/Banner/banner-flash-sale.tsx");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ./node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "./node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");

__webpack_require__.$Refresh$.runtime = /*#__PURE__*/ (C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0__, 2)));

function cov_1wv3w6yng4() {
  var path = "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\__fixtures__\\banner-flash-sale.fixtures.tsx";
  var hash = "1e2cd1d988a13c632de790d6664ea1fe86a1f58b";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\__fixtures__\\banner-flash-sale.fixtures.tsx",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 22
        },
        end: {
          line: 5,
          column: 1
        }
      },
      "1": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 4,
          column: 64
        }
      },
      "2": {
        start: {
          line: 6,
          column: 30
        },
        end: {
          line: 13,
          column: 1
        }
      },
      "3": {
        start: {
          line: 14,
          column: 28
        },
        end: {
          line: 23,
          column: 1
        }
      },
      "4": {
        start: {
          line: 24,
          column: 35
        },
        end: {
          line: 32,
          column: 1
        }
      },
      "5": {
        start: {
          line: 33,
          column: 37
        },
        end: {
          line: 43,
          column: 1
        }
      },
      "6": {
        start: {
          line: 44,
          column: 39
        },
        end: {
          line: 52,
          column: 1
        }
      },
      "7": {
        start: {
          line: 53,
          column: 28
        },
        end: {
          line: 63,
          column: 1
        }
      },
      "8": {
        start: {
          line: 64,
          column: 33
        },
        end: {
          line: 74,
          column: 1
        }
      },
      "9": {
        start: {
          line: 75,
          column: 27
        },
        end: {
          line: 104,
          column: 1
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 3,
            column: 22
          },
          end: {
            line: 3,
            column: 23
          }
        },
        loc: {
          start: {
            line: 3,
            column: 42
          },
          end: {
            line: 5,
            column: 1
          }
        },
        line: 3
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 3,
            column: 23
          },
          end: {
            line: 3,
            column: 39
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 3,
            column: 38
          },
          end: {
            line: 3,
            column: 39
          }
        }],
        line: 3
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0]
    },
    inputSourceMap: {
      version: 3,
      sources: ["C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\__fixtures__\\banner-flash-sale.fixtures.tsx"],
      sourcesContent: ["import FlashSaleBanner from \"../banner-flash-sale\";\r\n\r\n// Create fixture date that is 2 hours from when the component is rendered\r\nconst createEndTime = (hoursFromNow: number = 2) => {\r\n  return new Date(Date.now() + 1000 * 60 * 60 * hoursFromNow);\r\n};\r\n\r\nexport const defaultFixture = {\r\n  component: FlashSaleBanner,\r\n  props: {\r\n    endTime: createEndTime(),\r\n    title: \"FLASH SALE\",\r\n    subtitle: \"\u0E41\u0E2A\u0E07\u0E2D\u0E23\u0E38\u0E13\u0E19\u0E04\u0E23\u0E34\u0E19\u0E17\u0E23\u0E4C\",\r\n  },\r\n};\r\n\r\nexport const withDiscount = {\r\n  component: FlashSaleBanner,\r\n  props: {\r\n    endTime: createEndTime(),\r\n    title: \"FLASH SALE\",\r\n    subtitle: \"\u0E41\u0E2A\u0E07\u0E2D\u0E23\u0E38\u0E13\u0E19\u0E04\u0E23\u0E34\u0E19\u0E17\u0E23\u0E4C\",\r\n    discount: \"-11%\",\r\n    showDiscount: true,\r\n  },\r\n};\r\n\r\nexport const almostEndingFixture = {\r\n  component: FlashSaleBanner,\r\n  props: {\r\n    endTime: createEndTime(0.01), // 36 seconds from now\r\n    title: \"HURRY UP!\",\r\n    subtitle: \"\u0E42\u0E1B\u0E23\u0E42\u0E21\u0E0A\u0E31\u0E48\u0E19\u0E01\u0E33\u0E25\u0E31\u0E07\u0E08\u0E30\u0E2B\u0E21\u0E14\",\r\n    variant: \"danger\" as const,\r\n  },\r\n};\r\n\r\nexport const primaryVariantFixture = {\r\n  component: FlashSaleBanner,\r\n  props: {\r\n    endTime: createEndTime(),\r\n    title: \"MEMBERS SALE\",\r\n    subtitle: \"\u0E2A\u0E33\u0E2B\u0E23\u0E31\u0E1A\u0E2A\u0E21\u0E32\u0E0A\u0E34\u0E01\",\r\n    variant: \"primary\" as const,\r\n    discount: \"-15%\",\r\n    showDiscount: true,\r\n  },\r\n};\r\n\r\nexport const secondaryVariantFixture = {\r\n  component: FlashSaleBanner,\r\n  props: {\r\n    endTime: createEndTime(6), // 6 hours\r\n    title: \"WEEKEND SALE\",\r\n    subtitle: \"\u0E40\u0E09\u0E1E\u0E32\u0E30\u0E2A\u0E38\u0E14\u0E2A\u0E31\u0E1B\u0E14\u0E32\u0E2B\u0E4C\",\r\n    variant: \"secondary\" as const,\r\n  },\r\n};\r\n\r\nexport const largeFixture = {\r\n  component: FlashSaleBanner,\r\n  props: {\r\n    endTime: createEndTime(),\r\n    title: \"BIG FLASH SALE\",\r\n    subtitle: \"\u0E25\u0E14\u0E23\u0E32\u0E04\u0E32\u0E1E\u0E34\u0E40\u0E28\u0E29\",\r\n    size: \"lg\" as const,\r\n    discount: \"-20%\",\r\n    showDiscount: true,\r\n  },\r\n};\r\n\r\nexport const customIconFixture = {\r\n  component: FlashSaleBanner,\r\n  props: {\r\n    endTime: createEndTime(24), // 24 hours\r\n    title: \"24-HOUR SALE\",\r\n    subtitle: \"\u0E25\u0E14\u0E23\u0E32\u0E04\u0E32 24 \u0E0A\u0E31\u0E48\u0E27\u0E42\u0E21\u0E07\",\r\n    // This would be replaced by an actual icon in real implementation\r\n    icon: \"\u23F0\",\r\n    iconPosition: \"left\" as const,\r\n  },\r\n};\r\n\r\nexport const allFixtures = [\r\n  { name: \"Default\", ...defaultFixture },\r\n  { name: \"With Discount\", ...withDiscount },\r\n  { name: \"Almost Ending\", ...almostEndingFixture },\r\n  { name: \"Primary Variant\", ...primaryVariantFixture },\r\n  { name: \"Secondary Variant\", ...secondaryVariantFixture },\r\n  { name: \"Large Size\", ...largeFixture },\r\n  { name: \"Custom Icon\", ...customIconFixture },\r\n];"],
      names: ["FlashSaleBanner", "createEndTime", "hoursFromNow", "Date", "now", "defaultFixture", "component", "props", "endTime", "title", "subtitle", "withDiscount", "discount", "showDiscount", "almostEndingFixture", "variant", "primaryVariantFixture", "secondaryVariantFixture", "largeFixture", "size", "customIconFixture", "icon", "iconPosition", "allFixtures", "name"],
      mappings: "AAAA,OAAOA,qBAAqB,uBAAuB;AAEnD,0EAA0E;AAC1E,MAAMC,gBAAgB,CAACC,eAAuB,CAAC;IAC7C,OAAO,IAAIC,KAAKA,KAAKC,GAAG,KAAK,OAAO,KAAK,KAAKF;AAChD;AAEA,OAAO,MAAMG,iBAAiB;IAC5BC,WAAWN;IACXO,OAAO;QACLC,SAASP;QACTQ,OAAO;QACPC,UAAU;IACZ;AACF,EAAE;AAEF,OAAO,MAAMC,eAAe;IAC1BL,WAAWN;IACXO,OAAO;QACLC,SAASP;QACTQ,OAAO;QACPC,UAAU;QACVE,UAAU;QACVC,cAAc;IAChB;AACF,EAAE;AAEF,OAAO,MAAMC,sBAAsB;IACjCR,WAAWN;IACXO,OAAO;QACLC,SAASP,cAAc;QACvBQ,OAAO;QACPC,UAAU;QACVK,SAAS;IACX;AACF,EAAE;AAEF,OAAO,MAAMC,wBAAwB;IACnCV,WAAWN;IACXO,OAAO;QACLC,SAASP;QACTQ,OAAO;QACPC,UAAU;QACVK,SAAS;QACTH,UAAU;QACVC,cAAc;IAChB;AACF,EAAE;AAEF,OAAO,MAAMI,0BAA0B;IACrCX,WAAWN;IACXO,OAAO;QACLC,SAASP,cAAc;QACvBQ,OAAO;QACPC,UAAU;QACVK,SAAS;IACX;AACF,EAAE;AAEF,OAAO,MAAMG,eAAe;IAC1BZ,WAAWN;IACXO,OAAO;QACLC,SAASP;QACTQ,OAAO;QACPC,UAAU;QACVS,MAAM;QACNP,UAAU;QACVC,cAAc;IAChB;AACF,EAAE;AAEF,OAAO,MAAMO,oBAAoB;IAC/Bd,WAAWN;IACXO,OAAO;QACLC,SAASP,cAAc;QACvBQ,OAAO;QACPC,UAAU;QACV,kEAAkE;QAClEW,MAAM;QACNC,cAAc;IAChB;AACF,EAAE;AAEF,OAAO,MAAMC,cAAc;IACzB;QAAEC,MAAM;QAAW,GAAGnB,cAAc;IAAC;IACrC;QAAEmB,MAAM;QAAiB,GAAGb,YAAY;IAAC;IACzC;QAAEa,MAAM;QAAiB,GAAGV,mBAAmB;IAAC;IAChD;QAAEU,MAAM;QAAmB,GAAGR,qBAAqB;IAAC;IACpD;QAAEQ,MAAM;QAAqB,GAAGP,uBAAuB;IAAC;IACxD;QAAEO,MAAM;QAAc,GAAGN,YAAY;IAAC;IACtC;QAAEM,MAAM;QAAe,GAAGJ,iBAAiB;IAAC;CAC7C,CAAC"
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "1e2cd1d988a13c632de790d6664ea1fe86a1f58b"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1wv3w6yng4 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1wv3w6yng4();

// Create fixture date that is 2 hours from when the component is rendered
cov_1wv3w6yng4().s[0]++;
const createEndTime = (hoursFromNow = (cov_1wv3w6yng4().b[0][0]++, 2)) => {
  cov_1wv3w6yng4().f[0]++;
  cov_1wv3w6yng4().s[1]++;
  return new Date(Date.now() + 1000 * 60 * 60 * hoursFromNow);
};
const defaultFixture = (cov_1wv3w6yng4().s[2]++, {
  component: _banner_flash_sale__WEBPACK_IMPORTED_MODULE_1__["default"],
  props: {
    endTime: createEndTime(),
    title: "FLASH SALE",
    subtitle: "แสงอรุณนครินทร์"
  }
});
const withDiscount = (cov_1wv3w6yng4().s[3]++, {
  component: _banner_flash_sale__WEBPACK_IMPORTED_MODULE_1__["default"],
  props: {
    endTime: createEndTime(),
    title: "FLASH SALE",
    subtitle: "แสงอรุณนครินทร์",
    discount: "-11%",
    showDiscount: true
  }
});
const almostEndingFixture = (cov_1wv3w6yng4().s[4]++, {
  component: _banner_flash_sale__WEBPACK_IMPORTED_MODULE_1__["default"],
  props: {
    endTime: createEndTime(0.01),
    title: "HURRY UP!",
    subtitle: "โปรโมชั่นกำลังจะหมด",
    variant: "danger"
  }
});
const primaryVariantFixture = (cov_1wv3w6yng4().s[5]++, {
  component: _banner_flash_sale__WEBPACK_IMPORTED_MODULE_1__["default"],
  props: {
    endTime: createEndTime(),
    title: "MEMBERS SALE",
    subtitle: "สำหรับสมาชิก",
    variant: "primary",
    discount: "-15%",
    showDiscount: true
  }
});
const secondaryVariantFixture = (cov_1wv3w6yng4().s[6]++, {
  component: _banner_flash_sale__WEBPACK_IMPORTED_MODULE_1__["default"],
  props: {
    endTime: createEndTime(6),
    title: "WEEKEND SALE",
    subtitle: "เฉพาะสุดสัปดาห์",
    variant: "secondary"
  }
});
const largeFixture = (cov_1wv3w6yng4().s[7]++, {
  component: _banner_flash_sale__WEBPACK_IMPORTED_MODULE_1__["default"],
  props: {
    endTime: createEndTime(),
    title: "BIG FLASH SALE",
    subtitle: "ลดราคาพิเศษ",
    size: "lg",
    discount: "-20%",
    showDiscount: true
  }
});
const customIconFixture = (cov_1wv3w6yng4().s[8]++, {
  component: _banner_flash_sale__WEBPACK_IMPORTED_MODULE_1__["default"],
  props: {
    endTime: createEndTime(24),
    title: "24-HOUR SALE",
    subtitle: "ลดราคา 24 ชั่วโมง",
    // This would be replaced by an actual icon in real implementation
    icon: "⏰",
    iconPosition: "left"
  }
});
const allFixtures = (cov_1wv3w6yng4().s[9]++, [{
  name: "Default",
  ...defaultFixture
}, {
  name: "With Discount",
  ...withDiscount
}, {
  name: "Almost Ending",
  ...almostEndingFixture
}, {
  name: "Primary Variant",
  ...primaryVariantFixture
}, {
  name: "Secondary Variant",
  ...secondaryVariantFixture
}, {
  name: "Large Size",
  ...largeFixture
}, {
  name: "Custom Icon",
  ...customIconFixture
}]);

const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "./src/components/Banner/__stories__/banner-flash-sale.stories.tsx":
/*!*************************************************************************!*\
  !*** ./src/components/Banner/__stories__/banner-flash-sale.stories.tsx ***!
  \*************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

var C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0___namespace_cache;
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AccessibilityTest: () => (/* binding */ AccessibilityTest),
/* harmony export */   AlmostEnding: () => (/* binding */ AlmostEnding),
/* harmony export */   CustomIcon: () => (/* binding */ CustomIcon),
/* harmony export */   Default: () => (/* binding */ Default),
/* harmony export */   LargeSize: () => (/* binding */ LargeSize),
/* harmony export */   PrimaryVariant: () => (/* binding */ PrimaryVariant),
/* harmony export */   SecondaryVariant: () => (/* binding */ SecondaryVariant),
/* harmony export */   WithDiscount: () => (/* binding */ WithDiscount),
/* harmony export */   WithTimeEndCallback: () => (/* binding */ WithTimeEndCallback),
/* harmony export */   __namedExportsOrder: () => (/* binding */ __namedExportsOrder),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-refresh/runtime.js */ "./node_modules/react-refresh/runtime.js");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ "./node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var _banner_flash_sale__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../banner-flash-sale */ "./src/components/Banner/banner-flash-sale.tsx");
/* harmony import */ var _fixtures_banner_flash_sale_fixtures__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../__fixtures__/banner-flash-sale.fixtures */ "./src/components/Banner/__fixtures__/banner-flash-sale.fixtures.tsx");
/* harmony import */ var _storybook_test__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @storybook/test */ "./node_modules/@storybook/test/dist/index.mjs");
/* harmony import */ var _barrel_optimize_names_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock!=!lucide-react */ "./node_modules/lucide-react/dist/esm/icons/clock.js");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ./node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "./node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");

__webpack_require__.$Refresh$.runtime = /*#__PURE__*/ (C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0__, 2)));

/* eslint-disable */
// @ts-nocheck
// @ts-expect-error (Converted from ts-ignore)
var __STORY__ = "import type { Meta, StoryObj } from \"@storybook/react\";\r\nimport FlashSaleBanner from \"../banner-flash-sale\";\r\nimport { \r\n  defaultFixture,\r\n  withDiscount,\r\n  almostEndingFixture,\r\n  primaryVariantFixture,\r\n  secondaryVariantFixture,\r\n  largeFixture,\r\n  customIconFixture\r\n} from \"../__fixtures__/banner-flash-sale.fixtures\";\r\nimport { userEvent, within, expect } from \"@storybook/test\";\r\nimport { Clock } from \"lucide-react\";\r\n\r\n/**\r\n * Flash Sale Banner component displays a promotional banner with a countdown timer.\r\n * \r\n * This component is commonly used for limited-time promotions, such as flash sales,\r\n * temporary discounts, or special offers.\r\n */\r\nconst meta: Meta<typeof FlashSaleBanner> = {\r\n  title: \"UI/Banner/BannerFlashSale\",\r\n  component: FlashSaleBanner,\r\n  parameters: {\r\n    layout: \"centered\",\r\n    docs: {\r\n      description: {\r\n        component: \"A flash sale banner that displays countdown information for time-sensitive promotions.\"\r\n      }\r\n    },\r\n    a11y: {\r\n      config: {\r\n        // A11y audit rules\r\n        rules: [\r\n          {\r\n            id: \"color-contrast\",\r\n            reviewOnFail: true,\r\n          },\r\n          {\r\n            id: \"aria-valid-attr\",\r\n            reviewOnFail: true,\r\n          }\r\n        ],\r\n      },\r\n    },\r\n  },\r\n  argTypes: {\r\n    endTime: {\r\n      control: {\r\n        type: 'date'\r\n      },\r\n      description: \"The end time for the sale countdown\",\r\n      transform: (value: string) => {\r\n        if (!value) return new Date();\r\n        return new Date(value);\r\n      }\r\n    },\r\n    title: {\r\n      control: \"text\",\r\n      description: \"Main title text displayed in the banner\",\r\n    },\r\n    subtitle: {\r\n      control: \"text\",\r\n      description: \"Subtitle displayed above the main title\",\r\n    },\r\n    discount: {\r\n      control: \"text\",\r\n      description: \"Discount text to display (e.g., '-11%')\",\r\n    },\r\n    showDiscount: {\r\n      control: \"boolean\",\r\n      description: \"Whether to show the discount badge\",\r\n    },\r\n    variant: {\r\n      options: [\"default\", \"primary\", \"secondary\", \"danger\"],\r\n      control: { type: \"select\" },\r\n      description: \"Visual style variant of the banner\",\r\n    },\r\n    size: {\r\n      options: [\"sm\", \"default\", \"lg\"],\r\n      control: { type: \"select\" },\r\n      description: \"Size variant of the banner\",\r\n    },\r\n    iconPosition: {\r\n      options: [\"left\", \"right\"],\r\n      control: { type: \"radio\" },\r\n      description: \"Position of the icon relative to the title\",\r\n    },\r\n    onTimeEnd: {\r\n      action: \"Timer ended\",\r\n      description: \"Callback function when timer reaches zero\",\r\n    },\r\n    icon: {\r\n      control: { disable: true },\r\n      description: \"Custom icon element to display\",\r\n    },\r\n  },\r\n  args: {\r\n    ...defaultFixture.props,\r\n  },\r\n  tags: ['autodocs'],\r\n  decorators: [\r\n    (Story) => (\r\n      <div className=\"w-full max-w-2xl\">\r\n        <Story />\r\n      </div>\r\n    ),\r\n  ],\r\n};\r\n\r\nexport default meta;\r\ntype Story = StoryObj<typeof FlashSaleBanner>;\r\n\r\n/**\r\n * Default Flash Sale Banner with standard styling and configuration.\r\n */\r\nexport const Default: Story = {\r\n  args: {\r\n    ...defaultFixture.props,\r\n  },\r\n};\r\n\r\n/**\r\n * Flash Sale Banner with a discount badge.\r\n */\r\nexport const WithDiscount: Story = {\r\n  args: {\r\n    ...withDiscount.props,\r\n  },\r\n};\r\n\r\n/**\r\n * Flash Sale Banner with \"danger\" styling for urgency when time is almost up.\r\n */\r\nexport const AlmostEnding: Story = {\r\n  args: {\r\n    ...almostEndingFixture.props,\r\n  },\r\n};\r\n\r\n/**\r\n * Primary variant of the Flash Sale Banner.\r\n */\r\nexport const PrimaryVariant: Story = {\r\n  args: {\r\n    ...primaryVariantFixture.props,\r\n  },\r\n};\r\n\r\n/**\r\n * Secondary variant of the Flash Sale Banner.\r\n */\r\nexport const SecondaryVariant: Story = {\r\n  args: {\r\n    ...secondaryVariantFixture.props,\r\n  },\r\n};\r\n\r\n/**\r\n * Large size variant of the Flash Sale Banner.\r\n */\r\nexport const LargeSize: Story = {\r\n  args: {\r\n    ...largeFixture.props,\r\n  },\r\n};\r\n\r\n/**\r\n * Flash Sale Banner with custom icon and left positioning.\r\n */\r\nexport const CustomIcon: Story = {\r\n  args: {\r\n    ...customIconFixture.props,\r\n    icon: <Clock className=\"h-6 w-6 fill-current\" />,\r\n  },\r\n};\r\n\r\n/**\r\n * Interactive example showing the callback when timer ends.\r\n */\r\nexport const WithTimeEndCallback: Story = {\r\n  args: {\r\n    endTime: new Date(Date.now() + 5000), // 5 seconds from now\r\n    title: \"ENDING SOON\",\r\n    subtitle: \"โปรโมชั่นกำลังจะหมด\",\r\n    variant: \"danger\",\r\n  },\r\n  play: async ({ canvasElement, args }) => {\r\n    const canvas = within(canvasElement);\r\n    \r\n    // Wait for the timer to end (6 seconds to be safe)\r\n    await new Promise(resolve => setTimeout(resolve, 6000));\r\n    \r\n    // Verify that the callback was called\r\n    await expect(args.onTimeEnd).toHaveBeenCalled();\r\n  }\r\n};\r\n\r\n/**\r\n * Accessibility and keyboard navigation test.\r\n */\r\nexport const AccessibilityTest: Story = {\r\n  args: {\r\n    ...defaultFixture.props,\r\n  },\r\n  play: async ({ canvasElement }) => {\r\n    const canvas = within(canvasElement);\r\n    \r\n    // Focus the element\r\n    const banner = canvas.getByTestId(\"flash-sale-banner\");\r\n    await userEvent.tab();\r\n    \r\n    // Verify accessibility attributes\r\n    expect(banner).toHaveAttribute(\"role\", \"alert\");\r\n    expect(banner).toHaveAttribute(\"aria-live\", \"polite\");\r\n  }\r\n};";
// @ts-expect-error (Converted from ts-ignore)
var __LOCATIONS_MAP__ = {
  "Default": {
    "startLoc": {
      "col": 27,
      "line": 132
    },
    "endLoc": {
      "col": 1,
      "line": 136
    },
    "startBody": {
      "col": 27,
      "line": 132
    },
    "endBody": {
      "col": 1,
      "line": 136
    }
  },
  "WithDiscount": {
    "startLoc": {
      "col": 32,
      "line": 139
    },
    "endLoc": {
      "col": 1,
      "line": 143
    },
    "startBody": {
      "col": 32,
      "line": 139
    },
    "endBody": {
      "col": 1,
      "line": 143
    }
  },
  "AlmostEnding": {
    "startLoc": {
      "col": 32,
      "line": 146
    },
    "endLoc": {
      "col": 1,
      "line": 150
    },
    "startBody": {
      "col": 32,
      "line": 146
    },
    "endBody": {
      "col": 1,
      "line": 150
    }
  },
  "PrimaryVariant": {
    "startLoc": {
      "col": 34,
      "line": 153
    },
    "endLoc": {
      "col": 1,
      "line": 157
    },
    "startBody": {
      "col": 34,
      "line": 153
    },
    "endBody": {
      "col": 1,
      "line": 157
    }
  },
  "SecondaryVariant": {
    "startLoc": {
      "col": 36,
      "line": 160
    },
    "endLoc": {
      "col": 1,
      "line": 164
    },
    "startBody": {
      "col": 36,
      "line": 160
    },
    "endBody": {
      "col": 1,
      "line": 164
    }
  },
  "LargeSize": {
    "startLoc": {
      "col": 29,
      "line": 167
    },
    "endLoc": {
      "col": 1,
      "line": 171
    },
    "startBody": {
      "col": 29,
      "line": 167
    },
    "endBody": {
      "col": 1,
      "line": 171
    }
  },
  "CustomIcon": {
    "startLoc": {
      "col": 30,
      "line": 174
    },
    "endLoc": {
      "col": 1,
      "line": 185
    },
    "startBody": {
      "col": 30,
      "line": 174
    },
    "endBody": {
      "col": 1,
      "line": 185
    }
  },
  "WithTimeEndCallback": {
    "startLoc": {
      "col": 39,
      "line": 188
    },
    "endLoc": {
      "col": 1,
      "line": 202
    },
    "startBody": {
      "col": 39,
      "line": 188
    },
    "endBody": {
      "col": 1,
      "line": 202
    }
  },
  "AccessibilityTest": {
    "startLoc": {
      "col": 37,
      "line": 205
    },
    "endLoc": {
      "col": 1,
      "line": 218
    },
    "startBody": {
      "col": 37,
      "line": 205
    },
    "endBody": {
      "col": 1,
      "line": 218
    }
  }
};





/**
 * Flash Sale Banner component displays a promotional banner with a countdown timer.
 * 
 * This component is commonly used for limited-time promotions, such as flash sales,
 * temporary discounts, or special offers.
 */
const meta = {
  title: "UI/Banner/BannerFlashSale",
  component: _banner_flash_sale__WEBPACK_IMPORTED_MODULE_2__["default"],
  parameters: {
    "storySource": {
      "source": "import { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport FlashSaleBanner from \"../banner-flash-sale\";\nimport { defaultFixture, withDiscount, almostEndingFixture, primaryVariantFixture, secondaryVariantFixture, largeFixture, customIconFixture } from \"../__fixtures__/banner-flash-sale.fixtures\";\nimport { userEvent, within, expect } from \"@storybook/test\";\nimport { Clock } from \"__barrel_optimize__?names=Clock!=!lucide-react\";\n/**\r\n * Flash Sale Banner component displays a promotional banner with a countdown timer.\r\n * \r\n * This component is commonly used for limited-time promotions, such as flash sales,\r\n * temporary discounts, or special offers.\r\n */ const meta = {\n    title: \"UI/Banner/BannerFlashSale\",\n    component: FlashSaleBanner,\n    parameters: {\n        layout: \"centered\",\n        docs: {\n            description: {\n                component: \"A flash sale banner that displays countdown information for time-sensitive promotions.\"\n            }\n        },\n        a11y: {\n            config: {\n                // A11y audit rules\n                rules: [\n                    {\n                        id: \"color-contrast\",\n                        reviewOnFail: true\n                    },\n                    {\n                        id: \"aria-valid-attr\",\n                        reviewOnFail: true\n                    }\n                ]\n            }\n        }\n    },\n    argTypes: {\n        endTime: {\n            control: {\n                type: 'date'\n            },\n            description: \"The end time for the sale countdown\",\n            transform: (value)=>{\n                if (!value) return new Date();\n                return new Date(value);\n            }\n        },\n        title: {\n            control: \"text\",\n            description: \"Main title text displayed in the banner\"\n        },\n        subtitle: {\n            control: \"text\",\n            description: \"Subtitle displayed above the main title\"\n        },\n        discount: {\n            control: \"text\",\n            description: \"Discount text to display (e.g., '-11%')\"\n        },\n        showDiscount: {\n            control: \"boolean\",\n            description: \"Whether to show the discount badge\"\n        },\n        variant: {\n            options: [\n                \"default\",\n                \"primary\",\n                \"secondary\",\n                \"danger\"\n            ],\n            control: {\n                type: \"select\"\n            },\n            description: \"Visual style variant of the banner\"\n        },\n        size: {\n            options: [\n                \"sm\",\n                \"default\",\n                \"lg\"\n            ],\n            control: {\n                type: \"select\"\n            },\n            description: \"Size variant of the banner\"\n        },\n        iconPosition: {\n            options: [\n                \"left\",\n                \"right\"\n            ],\n            control: {\n                type: \"radio\"\n            },\n            description: \"Position of the icon relative to the title\"\n        },\n        onTimeEnd: {\n            action: \"Timer ended\",\n            description: \"Callback function when timer reaches zero\"\n        },\n        icon: {\n            control: {\n                disable: true\n            },\n            description: \"Custom icon element to display\"\n        }\n    },\n    args: {\n        ...defaultFixture.props\n    },\n    tags: [\n        'autodocs'\n    ],\n    decorators: [\n        (Story)=>/*#__PURE__*/ _jsxDEV(\"div\", {\n                className: \"w-full max-w-2xl\",\n                children: /*#__PURE__*/ _jsxDEV(Story, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fristJob\\\\sangaroon-nakharin-ecommerce\\\\frontend\\\\home\\\\src\\\\components\\\\Banner\\\\__stories__\\\\banner-flash-sale.stories.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fristJob\\\\sangaroon-nakharin-ecommerce\\\\frontend\\\\home\\\\src\\\\components\\\\Banner\\\\__stories__\\\\banner-flash-sale.stories.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this)\n    ]\n};\nexport default meta;\n/**\r\n * Default Flash Sale Banner with standard styling and configuration.\r\n */ export const Default = {\n    args: {\n        ...defaultFixture.props\n    }\n};\n/**\r\n * Flash Sale Banner with a discount badge.\r\n */ export const WithDiscount = {\n    args: {\n        ...withDiscount.props\n    }\n};\n/**\r\n * Flash Sale Banner with \"danger\" styling for urgency when time is almost up.\r\n */ export const AlmostEnding = {\n    args: {\n        ...almostEndingFixture.props\n    }\n};\n/**\r\n * Primary variant of the Flash Sale Banner.\r\n */ export const PrimaryVariant = {\n    args: {\n        ...primaryVariantFixture.props\n    }\n};\n/**\r\n * Secondary variant of the Flash Sale Banner.\r\n */ export const SecondaryVariant = {\n    args: {\n        ...secondaryVariantFixture.props\n    }\n};\n/**\r\n * Large size variant of the Flash Sale Banner.\r\n */ export const LargeSize = {\n    args: {\n        ...largeFixture.props\n    }\n};\n/**\r\n * Flash Sale Banner with custom icon and left positioning.\r\n */ export const CustomIcon = {\n    args: {\n        ...customIconFixture.props,\n        icon: /*#__PURE__*/ _jsxDEV(Clock, {\n            className: \"h-6 w-6 fill-current\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\fristJob\\\\sangaroon-nakharin-ecommerce\\\\frontend\\\\home\\\\src\\\\components\\\\Banner\\\\__stories__\\\\banner-flash-sale.stories.tsx\",\n            lineNumber: 174,\n            columnNumber: 11\n        }, this)\n    }\n};\n/**\r\n * Interactive example showing the callback when timer ends.\r\n */ export const WithTimeEndCallback = {\n    args: {\n        endTime: new Date(Date.now() + 5000),\n        title: \"ENDING SOON\",\n        subtitle: \"โปรโมชั่นกำลังจะหมด\",\n        variant: \"danger\"\n    },\n    play: async ({ canvasElement, args })=>{\n        const canvas = within(canvasElement);\n        // Wait for the timer to end (6 seconds to be safe)\n        await new Promise((resolve)=>setTimeout(resolve, 6000));\n        // Verify that the callback was called\n        await expect(args.onTimeEnd).toHaveBeenCalled();\n    }\n};\n/**\r\n * Accessibility and keyboard navigation test.\r\n */ export const AccessibilityTest = {\n    args: {\n        ...defaultFixture.props\n    },\n    play: async ({ canvasElement })=>{\n        const canvas = within(canvasElement);\n        // Focus the element\n        const banner = canvas.getByTestId(\"flash-sale-banner\");\n        await userEvent.tab();\n        // Verify accessibility attributes\n        expect(banner).toHaveAttribute(\"role\", \"alert\");\n        expect(banner).toHaveAttribute(\"aria-live\", \"polite\");\n    }\n};\n",
      "locationsMap": {
        "default": {
          "startLoc": {
            "col": 27,
            "line": 132
          },
          "endLoc": {
            "col": 1,
            "line": 136
          },
          "startBody": {
            "col": 27,
            "line": 132
          },
          "endBody": {
            "col": 1,
            "line": 136
          }
        },
        "with-discount": {
          "startLoc": {
            "col": 32,
            "line": 139
          },
          "endLoc": {
            "col": 1,
            "line": 143
          },
          "startBody": {
            "col": 32,
            "line": 139
          },
          "endBody": {
            "col": 1,
            "line": 143
          }
        },
        "almost-ending": {
          "startLoc": {
            "col": 32,
            "line": 146
          },
          "endLoc": {
            "col": 1,
            "line": 150
          },
          "startBody": {
            "col": 32,
            "line": 146
          },
          "endBody": {
            "col": 1,
            "line": 150
          }
        },
        "primary-variant": {
          "startLoc": {
            "col": 34,
            "line": 153
          },
          "endLoc": {
            "col": 1,
            "line": 157
          },
          "startBody": {
            "col": 34,
            "line": 153
          },
          "endBody": {
            "col": 1,
            "line": 157
          }
        },
        "secondary-variant": {
          "startLoc": {
            "col": 36,
            "line": 160
          },
          "endLoc": {
            "col": 1,
            "line": 164
          },
          "startBody": {
            "col": 36,
            "line": 160
          },
          "endBody": {
            "col": 1,
            "line": 164
          }
        },
        "large-size": {
          "startLoc": {
            "col": 29,
            "line": 167
          },
          "endLoc": {
            "col": 1,
            "line": 171
          },
          "startBody": {
            "col": 29,
            "line": 167
          },
          "endBody": {
            "col": 1,
            "line": 171
          }
        },
        "custom-icon": {
          "startLoc": {
            "col": 30,
            "line": 174
          },
          "endLoc": {
            "col": 1,
            "line": 185
          },
          "startBody": {
            "col": 30,
            "line": 174
          },
          "endBody": {
            "col": 1,
            "line": 185
          }
        },
        "with-time-end-callback": {
          "startLoc": {
            "col": 39,
            "line": 188
          },
          "endLoc": {
            "col": 1,
            "line": 202
          },
          "startBody": {
            "col": 39,
            "line": 188
          },
          "endBody": {
            "col": 1,
            "line": 202
          }
        },
        "accessibility-test": {
          "startLoc": {
            "col": 37,
            "line": 205
          },
          "endLoc": {
            "col": 1,
            "line": 218
          },
          "startBody": {
            "col": 37,
            "line": 205
          },
          "endBody": {
            "col": 1,
            "line": 218
          }
        }
      }
    },
    layout: "centered",
    docs: {
      description: {
        component: "A flash sale banner that displays countdown information for time-sensitive promotions."
      }
    },
    a11y: {
      config: {
        // A11y audit rules
        rules: [{
          id: "color-contrast",
          reviewOnFail: true
        }, {
          id: "aria-valid-attr",
          reviewOnFail: true
        }]
      }
    }
  },
  argTypes: {
    endTime: {
      control: {
        type: 'date'
      },
      description: "The end time for the sale countdown",
      transform: value => {
        if (!value) return new Date();
        return new Date(value);
      }
    },
    title: {
      control: "text",
      description: "Main title text displayed in the banner"
    },
    subtitle: {
      control: "text",
      description: "Subtitle displayed above the main title"
    },
    discount: {
      control: "text",
      description: "Discount text to display (e.g., '-11%')"
    },
    showDiscount: {
      control: "boolean",
      description: "Whether to show the discount badge"
    },
    variant: {
      options: ["default", "primary", "secondary", "danger"],
      control: {
        type: "select"
      },
      description: "Visual style variant of the banner"
    },
    size: {
      options: ["sm", "default", "lg"],
      control: {
        type: "select"
      },
      description: "Size variant of the banner"
    },
    iconPosition: {
      options: ["left", "right"],
      control: {
        type: "radio"
      },
      description: "Position of the icon relative to the title"
    },
    onTimeEnd: {
      action: "Timer ended",
      description: "Callback function when timer reaches zero"
    },
    icon: {
      control: {
        disable: true
      },
      description: "Custom icon element to display"
    }
  },
  args: {
    ..._fixtures_banner_flash_sale_fixtures__WEBPACK_IMPORTED_MODULE_3__.defaultFixture.props
  },
  tags: ['autodocs'],
  decorators: [Story => /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
    className: "w-full max-w-2xl",
    children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(Story, {}, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\__stories__\\banner-flash-sale.stories.tsx",
      lineNumber: 105,
      columnNumber: 9
    }, undefined)
  }, void 0, false, {
    fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\__stories__\\banner-flash-sale.stories.tsx",
    lineNumber: 104,
    columnNumber: 7
  }, undefined)]
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (meta);
/**
 * Default Flash Sale Banner with standard styling and configuration.
 */
const Default = {
  args: {
    ..._fixtures_banner_flash_sale_fixtures__WEBPACK_IMPORTED_MODULE_3__.defaultFixture.props
  }
};
;
/**
 * Flash Sale Banner with a discount badge.
 */
const WithDiscount = {
  args: {
    ..._fixtures_banner_flash_sale_fixtures__WEBPACK_IMPORTED_MODULE_3__.withDiscount.props
  }
};
;
/**
 * Flash Sale Banner with "danger" styling for urgency when time is almost up.
 */
const AlmostEnding = {
  args: {
    ..._fixtures_banner_flash_sale_fixtures__WEBPACK_IMPORTED_MODULE_3__.almostEndingFixture.props
  }
};
;
/**
 * Primary variant of the Flash Sale Banner.
 */
const PrimaryVariant = {
  args: {
    ..._fixtures_banner_flash_sale_fixtures__WEBPACK_IMPORTED_MODULE_3__.primaryVariantFixture.props
  }
};
;
/**
 * Secondary variant of the Flash Sale Banner.
 */
const SecondaryVariant = {
  args: {
    ..._fixtures_banner_flash_sale_fixtures__WEBPACK_IMPORTED_MODULE_3__.secondaryVariantFixture.props
  }
};
;
/**
 * Large size variant of the Flash Sale Banner.
 */
const LargeSize = {
  args: {
    ..._fixtures_banner_flash_sale_fixtures__WEBPACK_IMPORTED_MODULE_3__.largeFixture.props
  }
};
;
/**
 * Flash Sale Banner with custom icon and left positioning.
 */
const CustomIcon = {
  args: {
    ..._fixtures_banner_flash_sale_fixtures__WEBPACK_IMPORTED_MODULE_3__.customIconFixture.props,
    icon: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_Clock_lucide_react__WEBPACK_IMPORTED_MODULE_5__["default"], {
      className: "h-6 w-6 fill-current"
    }, void 0, false, {
      fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\__stories__\\banner-flash-sale.stories.tsx",
      lineNumber: 174,
      columnNumber: 11
    }, undefined)
  }
};
;
/**
 * Interactive example showing the callback when timer ends.
 */
const WithTimeEndCallback = {
  args: {
    endTime: new Date(Date.now() + 5000),
    title: "ENDING SOON",
    subtitle: "โปรโมชั่นกำลังจะหมด",
    variant: "danger"
  },
  play: async ({
    canvasElement,
    args
  }) => {
    const canvas = (0,_storybook_test__WEBPACK_IMPORTED_MODULE_4__.within)(canvasElement);
    // Wait for the timer to end (6 seconds to be safe)
    await new Promise(resolve => setTimeout(resolve, 6000));
    // Verify that the callback was called
    await (0,_storybook_test__WEBPACK_IMPORTED_MODULE_4__.expect)(args.onTimeEnd).toHaveBeenCalled();
  }
};
;
/**
 * Accessibility and keyboard navigation test.
 */
const AccessibilityTest = {
  args: {
    ..._fixtures_banner_flash_sale_fixtures__WEBPACK_IMPORTED_MODULE_3__.defaultFixture.props
  },
  play: async ({
    canvasElement
  }) => {
    const canvas = (0,_storybook_test__WEBPACK_IMPORTED_MODULE_4__.within)(canvasElement);
    // Focus the element
    const banner = canvas.getByTestId("flash-sale-banner");
    await _storybook_test__WEBPACK_IMPORTED_MODULE_4__.userEvent.tab();
    // Verify accessibility attributes
    (0,_storybook_test__WEBPACK_IMPORTED_MODULE_4__.expect)(banner).toHaveAttribute("role", "alert");
    (0,_storybook_test__WEBPACK_IMPORTED_MODULE_4__.expect)(banner).toHaveAttribute("aria-live", "polite");
  }
};
;
const __namedExportsOrder = ["Default", "WithDiscount", "AlmostEnding", "PrimaryVariant", "SecondaryVariant", "LargeSize", "CustomIcon", "WithTimeEndCallback", "AccessibilityTest"];
Default.parameters = {
  ...Default.parameters,
  docs: {
    ...Default.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ...defaultFixture.props\n  }\n}",
      ...Default.parameters?.docs?.source
    },
    description: {
      story: "Default Flash Sale Banner with standard styling and configuration.",
      ...Default.parameters?.docs?.description
    }
  }
};
WithDiscount.parameters = {
  ...WithDiscount.parameters,
  docs: {
    ...WithDiscount.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ...withDiscount.props\n  }\n}",
      ...WithDiscount.parameters?.docs?.source
    },
    description: {
      story: "Flash Sale Banner with a discount badge.",
      ...WithDiscount.parameters?.docs?.description
    }
  }
};
AlmostEnding.parameters = {
  ...AlmostEnding.parameters,
  docs: {
    ...AlmostEnding.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ...almostEndingFixture.props\n  }\n}",
      ...AlmostEnding.parameters?.docs?.source
    },
    description: {
      story: "Flash Sale Banner with \"danger\" styling for urgency when time is almost up.",
      ...AlmostEnding.parameters?.docs?.description
    }
  }
};
PrimaryVariant.parameters = {
  ...PrimaryVariant.parameters,
  docs: {
    ...PrimaryVariant.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ...primaryVariantFixture.props\n  }\n}",
      ...PrimaryVariant.parameters?.docs?.source
    },
    description: {
      story: "Primary variant of the Flash Sale Banner.",
      ...PrimaryVariant.parameters?.docs?.description
    }
  }
};
SecondaryVariant.parameters = {
  ...SecondaryVariant.parameters,
  docs: {
    ...SecondaryVariant.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ...secondaryVariantFixture.props\n  }\n}",
      ...SecondaryVariant.parameters?.docs?.source
    },
    description: {
      story: "Secondary variant of the Flash Sale Banner.",
      ...SecondaryVariant.parameters?.docs?.description
    }
  }
};
LargeSize.parameters = {
  ...LargeSize.parameters,
  docs: {
    ...LargeSize.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ...largeFixture.props\n  }\n}",
      ...LargeSize.parameters?.docs?.source
    },
    description: {
      story: "Large size variant of the Flash Sale Banner.",
      ...LargeSize.parameters?.docs?.description
    }
  }
};
CustomIcon.parameters = {
  ...CustomIcon.parameters,
  docs: {
    ...CustomIcon.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ...customIconFixture.props,\n    icon: <Clock className=\"h-6 w-6 fill-current\" />\n  }\n}",
      ...CustomIcon.parameters?.docs?.source
    },
    description: {
      story: "Flash Sale Banner with custom icon and left positioning.",
      ...CustomIcon.parameters?.docs?.description
    }
  }
};
WithTimeEndCallback.parameters = {
  ...WithTimeEndCallback.parameters,
  docs: {
    ...WithTimeEndCallback.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    endTime: new Date(Date.now() + 5000),\n    // 5 seconds from now\n    title: \"ENDING SOON\",\n    subtitle: \"\u0E42\u0E1B\u0E23\u0E42\u0E21\u0E0A\u0E31\u0E48\u0E19\u0E01\u0E33\u0E25\u0E31\u0E07\u0E08\u0E30\u0E2B\u0E21\u0E14\",\n    variant: \"danger\"\n  },\n  play: async ({\n    canvasElement,\n    args\n  }) => {\n    const canvas = within(canvasElement);\n\n    // Wait for the timer to end (6 seconds to be safe)\n    await new Promise(resolve => setTimeout(resolve, 6000));\n\n    // Verify that the callback was called\n    await expect(args.onTimeEnd).toHaveBeenCalled();\n  }\n}",
      ...WithTimeEndCallback.parameters?.docs?.source
    },
    description: {
      story: "Interactive example showing the callback when timer ends.",
      ...WithTimeEndCallback.parameters?.docs?.description
    }
  }
};
AccessibilityTest.parameters = {
  ...AccessibilityTest.parameters,
  docs: {
    ...AccessibilityTest.parameters?.docs,
    source: {
      originalSource: "{\n  args: {\n    ...defaultFixture.props\n  },\n  play: async ({\n    canvasElement\n  }) => {\n    const canvas = within(canvasElement);\n\n    // Focus the element\n    const banner = canvas.getByTestId(\"flash-sale-banner\");\n    await userEvent.tab();\n\n    // Verify accessibility attributes\n    expect(banner).toHaveAttribute(\"role\", \"alert\");\n    expect(banner).toHaveAttribute(\"aria-live\", \"polite\");\n  }\n}",
      ...AccessibilityTest.parameters?.docs?.source
    },
    description: {
      story: "Accessibility and keyboard navigation test.",
      ...AccessibilityTest.parameters?.docs?.description
    }
  }
};

const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ }),

/***/ "./src/components/Banner/banner-flash-sale.tsx":
/*!*****************************************************!*\
  !*** ./src/components/Banner/banner-flash-sale.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

var C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0___namespace_cache;
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/react-refresh/runtime.js */ "./node_modules/react-refresh/runtime.js");
/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ "./node_modules/next/dist/compiled/react/jsx-dev-runtime.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! class-variance-authority */ "./node_modules/class-variance-authority/dist/index.mjs");
/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ "./src/lib/utils.ts");
/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ "./node_modules/@storybook/nextjs/dist/images/next-image.mjs");
/* provided dependency */ var __react_refresh_utils__ = __webpack_require__(/*! ./node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js */ "./node_modules/@pmmmwh/react-refresh-webpack-plugin/lib/runtime/RefreshUtils.js");

__webpack_require__.$Refresh$.runtime = /*#__PURE__*/ (C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(C_Users_ASUS_Desktop_fristJob_sangaroon_nakharin_ecommerce_frontend_home_node_modules_react_refresh_runtime_js__WEBPACK_IMPORTED_MODULE_0__, 2)));

function cov_efvd3qzmv() {
  var path = "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\banner-flash-sale.tsx";
  var hash = "134a8302c954d1b06bde92650a54f48d5f39e2fa";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\banner-flash-sale.tsx",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 9
        },
        end: {
          line: 2,
          column: 23
        }
      },
      "1": {
        start: {
          line: 8,
          column: 32
        },
        end: {
          line: 26,
          column: 2
        }
      },
      "2": {
        start: {
          line: 28,
          column: 25
        },
        end: {
          line: 40,
          column: 2
        }
      },
      "3": {
        start: {
          line: 42,
          column: 30
        },
        end: {
          line: 54,
          column: 2
        }
      },
      "4": {
        start: {
          line: 55,
          column: 24
        },
        end: {
          line: 259,
          column: 1
        }
      },
      "5": {
        start: {
          line: 66,
          column: 4
        },
        end: {
          line: 66,
          column: 9
        }
      },
      "6": {
        start: {
          line: 67,
          column: 36
        },
        end: {
          line: 67,
          column: 65
        }
      },
      "7": {
        start: {
          line: 69,
          column: 28
        },
        end: {
          line: 69,
          column: 81
        }
      },
      "8": {
        start: {
          line: 70,
          column: 27
        },
        end: {
          line: 70,
          column: 71
        }
      },
      "9": {
        start: {
          line: 71,
          column: 8
        },
        end: {
          line: 80,
          column: 9
        }
      },
      "10": {
        start: {
          line: 72,
          column: 12
        },
        end: {
          line: 74,
          column: 13
        }
      },
      "11": {
        start: {
          line: 73,
          column: 16
        },
        end: {
          line: 73,
          column: 28
        }
      },
      "12": {
        start: {
          line: 75,
          column: 12
        },
        end: {
          line: 79,
          column: 14
        }
      },
      "13": {
        start: {
          line: 81,
          column: 8
        },
        end: {
          line: 85,
          column: 10
        }
      },
      "14": {
        start: {
          line: 87,
          column: 4
        },
        end: {
          line: 100,
          column: 7
        }
      },
      "15": {
        start: {
          line: 89,
          column: 26
        },
        end: {
          line: 93,
          column: 55
        }
      },
      "16": {
        start: {
          line: 91,
          column: 20
        },
        end: {
          line: 91,
          column: 53
        }
      },
      "17": {
        start: {
          line: 94,
          column: 12
        },
        end: {
          line: 96,
          column: 44
        }
      },
      "18": {
        start: {
          line: 95,
          column: 49
        },
        end: {
          line: 95,
          column: 69
        }
      },
      "19": {
        start: {
          line: 101,
          column: 4
        },
        end: {
          line: 258,
          column: 13
        }
      },
      "20": {
        start: {
          line: 260,
          column: 0
        },
        end: {
          line: 260,
          column: 52
        }
      },
      "21": {
        start: {
          line: 261,
          column: 0
        },
        end: {
          line: 261,
          column: 21
        }
      },
      "22": {
        start: {
          line: 263,
          column: 0
        },
        end: {
          line: 398,
          column: 2
        }
      },
      "23": {
        start: {
          line: 400,
          column: 0
        },
        end: {
          line: 400,
          column: 36
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 55,
            column: 24
          },
          end: {
            line: 55,
            column: 25
          }
        },
        loc: {
          start: {
            line: 65,
            column: 67
          },
          end: {
            line: 259,
            column: 1
          }
        },
        line: 65
      },
      "1": {
        name: "calculateTimeLeft",
        decl: {
          start: {
            line: 68,
            column: 13
          },
          end: {
            line: 68,
            column: 30
          }
        },
        loc: {
          start: {
            line: 68,
            column: 33
          },
          end: {
            line: 86,
            column: 5
          }
        },
        line: 68
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 88,
            column: 37
          },
          end: {
            line: 88,
            column: 38
          }
        },
        loc: {
          start: {
            line: 88,
            column: 41
          },
          end: {
            line: 97,
            column: 9
          }
        },
        line: 88
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 90,
            column: 51
          },
          end: {
            line: 90,
            column: 52
          }
        },
        loc: {
          start: {
            line: 90,
            column: 55
          },
          end: {
            line: 92,
            column: 17
          }
        },
        line: 90
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 95,
            column: 45
          },
          end: {
            line: 95,
            column: 46
          }
        },
        loc: {
          start: {
            line: 95,
            column: 49
          },
          end: {
            line: 95,
            column: 69
          }
        },
        line: 95
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 55,
            column: 62
          },
          end: {
            line: 55,
            column: 82
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 55,
            column: 70
          },
          end: {
            line: 55,
            column: 82
          }
        }],
        line: 55
      },
      "1": {
        loc: {
          start: {
            line: 55,
            column: 84
          },
          end: {
            line: 55,
            column: 112
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 55,
            column: 95
          },
          end: {
            line: 55,
            column: 112
          }
        }],
        line: 55
      },
      "2": {
        loc: {
          start: {
            line: 55,
            column: 114
          },
          end: {
            line: 55,
            column: 131
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 55,
            column: 125
          },
          end: {
            line: 55,
            column: 131
          }
        }],
        line: 55
      },
      "3": {
        loc: {
          start: {
            line: 55,
            column: 133
          },
          end: {
            line: 55,
            column: 153
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 55,
            column: 148
          },
          end: {
            line: 55,
            column: 153
          }
        }],
        line: 55
      },
      "4": {
        loc: {
          start: {
            line: 55,
            column: 166
          },
          end: {
            line: 65,
            column: 8
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 55,
            column: 187
          },
          end: {
            line: 65,
            column: 8
          }
        }],
        line: 55
      },
      "5": {
        loc: {
          start: {
            line: 65,
            column: 10
          },
          end: {
            line: 65,
            column: 32
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 65,
            column: 25
          },
          end: {
            line: 65,
            column: 32
          }
        }],
        line: 65
      },
      "6": {
        loc: {
          start: {
            line: 65,
            column: 34
          },
          end: {
            line: 65,
            column: 62
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 65,
            column: 43
          },
          end: {
            line: 65,
            column: 62
          }
        }],
        line: 65
      },
      "7": {
        loc: {
          start: {
            line: 69,
            column: 28
          },
          end: {
            line: 69,
            column: 81
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 69,
            column: 54
          },
          end: {
            line: 69,
            column: 61
          }
        }, {
          start: {
            line: 69,
            column: 64
          },
          end: {
            line: 69,
            column: 81
          }
        }],
        line: 69
      },
      "8": {
        loc: {
          start: {
            line: 71,
            column: 8
          },
          end: {
            line: 80,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 71,
            column: 8
          },
          end: {
            line: 80,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 71
      },
      "9": {
        loc: {
          start: {
            line: 72,
            column: 12
          },
          end: {
            line: 74,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 72,
            column: 12
          },
          end: {
            line: 74,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 72
      },
      "10": {
        loc: {
          start: {
            line: 116,
            column: 24
          },
          end: {
            line: 123,
            column: 32
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 116,
            column: 24
          },
          end: {
            line: 116,
            column: 47
          }
        }, {
          start: {
            line: 116,
            column: 65
          },
          end: {
            line: 123,
            column: 32
          }
        }],
        line: 116
      },
      "11": {
        loc: {
          start: {
            line: 176,
            column: 24
          },
          end: {
            line: 185,
            column: 32
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 176,
            column: 24
          },
          end: {
            line: 176,
            column: 36
          }
        }, {
          start: {
            line: 176,
            column: 54
          },
          end: {
            line: 185,
            column: 32
          }
        }],
        line: 176
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0],
      "3": [0],
      "4": [0],
      "5": [0],
      "6": [0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0]
    },
    inputSourceMap: {
      version: 3,
      sources: ["C:/Users/<USER>/Desktop/fristJob/sangaroon-nakharin-ecommerce/frontend/home/<USER>/components/Banner/banner-flash-sale.tsx"],
      sourcesContent: ["import { useState, useEffect, ReactNode } from 'react';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\nimport { cn } from '@/lib/utils';\r\nimport Image from 'next/image';\r\n\r\n// Define variants using class-variance-authority\r\nconst flashSaleBannerVariants = cva('flex items-center rounded-card border gap-2 w-full', {\r\n  variants: {\r\n    variant: {\r\n      default: 'border-secondary bg-secondary-bg text-black',\r\n      primary: 'border-blue-400 bg-blue-50 text-black',\r\n      secondary: 'border-purple-400 bg-purple-50 text-black',\r\n      danger: 'border-red-400 bg-red-50 text-black',\r\n    },\r\n    size: {\r\n      sm: 'text-sm min-h-[80px]',\r\n      default: 'text-base sm:h-[96px] sm:w-[548px]', // Fixed size only on sm and above\r\n      lg: 'text-lg min-h-[112px]',\r\n    },\r\n  },\r\n  defaultVariants: {\r\n    variant: 'default',\r\n    size: 'default',\r\n  },\r\n});\r\n\r\n// Timer box\r\nconst timerBoxVariants = cva('font-bold px-3 py-2 text-xl rounded-sm', {\r\n  variants: {\r\n    variant: {\r\n      default: 'bg-[#FFD904] text-black',\r\n      primary: 'bg-blue-400 text-white',\r\n      secondary: 'bg-purple-400 text-white',\r\n      danger: 'bg-red-400 text-white',\r\n    },\r\n  },\r\n  defaultVariants: {\r\n    variant: 'default',\r\n  },\r\n});\r\n\r\n// Discount badge\r\nconst discountBadgeVariants = cva('px-2 py-1 rounded-full text-xs font-bold', {\r\n  variants: {\r\n    variant: {\r\n      default: 'bg-red-500 text-white',\r\n      primary: 'bg-blue-600 text-white',\r\n      secondary: 'bg-purple-600 text-white',\r\n      danger: 'bg-red-600 text-white',\r\n    },\r\n  },\r\n  defaultVariants: {\r\n    variant: 'default',\r\n  },\r\n});\r\n\r\n// Props\r\nexport interface FlashSaleBannerProps extends VariantProps<typeof flashSaleBannerVariants> {\r\n  className?: string;\r\n  endTime: Date | string;\r\n  title?: string;\r\n  subtitle?: string;\r\n  discount?: string;\r\n  showDiscount?: boolean;\r\n  onTimeEnd?: () => void;\r\n  icon?: ReactNode;\r\n  iconPosition?: 'left' | 'right';\r\n  testId?: string;\r\n}\r\n\r\nconst FlashSaleBanner = ({\r\n  className,\r\n  variant,\r\n  size,\r\n  endTime,\r\n  title = 'FLASH SALE',\r\n  subtitle = '\u0E41\u0E2A\u0E07\u0E2D\u0E23\u0E38\u0E13\u0E19\u0E04\u0E23\u0E34\u0E19\u0E17\u0E23\u0E4C',\r\n  discount = '-11%',\r\n  showDiscount = false,\r\n  onTimeEnd,\r\n  icon = (\r\n    <Image\r\n      src=\"/images/lightning.png\"\r\n      alt=\"Flash sale icon\"\r\n      width={52}\r\n      height={52}\r\n      className=\"h-7 w-7 object-contain sm:h-[52px] sm:w-[52px]\"\r\n    />\r\n  ),\r\n  iconPosition = 'right',\r\n  testId = 'flash-sale-banner',\r\n}: FlashSaleBannerProps) => {\r\n  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());\r\n\r\n  function calculateTimeLeft() {\r\n    const endDateTime = endTime instanceof Date ? endTime : new Date(endTime);\r\n    const difference = endDateTime.getTime() - new Date().getTime();\r\n\r\n    if (difference <= 0) {\r\n      if (onTimeEnd) {\r\n        onTimeEnd();\r\n      }\r\n      return { hours: '00', minutes: '00', seconds: '00' };\r\n    }\r\n\r\n    return {\r\n      hours: Math.floor((difference / (1000 * 60 * 60)) % 24)\r\n        .toString()\r\n        .padStart(2, '0'),\r\n      minutes: Math.floor((difference / 1000 / 60) % 60)\r\n        .toString()\r\n        .padStart(2, '0'),\r\n      seconds: Math.floor((difference / 1000) % 60)\r\n        .toString()\r\n        .padStart(2, '0'),\r\n    };\r\n  }\r\n\r\n  useEffect(() => {\r\n    const timer = setInterval(() => {\r\n      setTimeLeft(calculateTimeLeft());\r\n    }, 1000);\r\n\r\n    return () => clearInterval(timer);\r\n  }, [endTime]);\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        flashSaleBannerVariants({ variant, size, className }),\r\n        'mx-auto flex-col sm:flex-row',\r\n      )}\r\n      data-testid={testId}\r\n      role=\"alert\"\r\n      aria-live=\"polite\"\r\n    >\r\n      <div className=\"flex h-full w-full flex-col items-center gap-3 px-4 text-center sm:flex-row sm:justify-between sm:px-4  sm:text-left\">\r\n        {/* Text & icon */}\r\n        <div className=\"flex flex-col items-center sm:flex-row sm:items-center\">\r\n          {iconPosition === 'left' && <div className=\"text-current\">{icon}</div>}\r\n          <div className=\"font-bold sm:ml-2\">\r\n            <span className=\" sm:text-body-16-none block text-title text-sm font-medium\">\r\n              {subtitle}\r\n            </span>\r\n            <div className=\"flex items-center justify-center sm:justify-start\">\r\n              <span className=\"text-text-lg-bold sm:text-text-3xl flex items-center\">\r\n                FLASH\r\n                <span className=\"-mx-1 -mt-1 inline-flex items-start justify-center sm:-mx-2\">\r\n                  {icon}\r\n                </span>\r\n                SALE\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Timer & discount */}\r\n        <div className=\"flex flex-col items-center gap-2 sm:flex-row sm:items-center\">\r\n          {showDiscount && <div className={cn(discountBadgeVariants({ variant }))}>{discount}</div>}\r\n          <div\r\n            className=\"flex\"\r\n            aria-label={`Time remaining: ${timeLeft.hours} hours, ${timeLeft.minutes} minutes, and ${timeLeft.seconds} seconds`}\r\n          >\r\n            <div className={cn(timerBoxVariants({ variant }))}>{timeLeft.hours}</div>\r\n            <div className=\"flex items-center px-1 text-xl font-bold text-current\">:</div>\r\n            <div className={cn(timerBoxVariants({ variant }))}>{timeLeft.minutes}</div>\r\n            <div className=\"flex items-center px-1 text-xl font-bold text-current\">:</div>\r\n            <div className={cn(timerBoxVariants({ variant }))}>{timeLeft.seconds}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FlashSaleBanner;\r\n"],
      names: [],
      mappings: ";;AAAA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AAAW,CAAV,AAAW,CAAV,AAAW,CAAV,AAAW,CAAV,AAAW,CAAV,AAAW,CAAV,AAAW,CAAV,AAAW,CAAC,AAAX,CAAC,CAAC;AAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AAAmB,CAAlB,AAAmB,CAAlB,AAAmB,CAAlB,AAAmB,CAAlB,AAAmB,CAAC,AAAnB,CAAoB,AAAnB,CAAoB,AAAnB,CAAoB,AAAnB,CAAoB,AAAnB,CAAoB,AAAnB,CAAoB,AAAnB,CAAC,AAAmB,CAAlB,AAAmB,CAAlB,AAAmB,CAAlB,AAAmB,CAAC,AAAnB,CAAC,AAAmB,CAAlB,AAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAE/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC;AAgBH,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACR,CAAC,CAAC,CAAC,CAAC,CAAC,CACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aACN,QAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;QAE7D,CAAC,CACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAE9D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEhE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAAC,CAAC,CAAC;QACvD,CAAC;QAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,AAAE,CAAD,AAAE,CAAC,CAAC,CACpD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AAAC,CAAC,CAAC,CAAC,CAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,AAAC,CAAC,CAAC,CAAC,CAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC;IACJ,CAAC;IAED,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;qCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;mDAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACnC,CAAC,CAAC;kDAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAET,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;6CAAC,CAAC,CAAC,CAAC,CAAC,AAAE,CAAD,AAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QACpC,CAAC,CAAC;oCAAC;QAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KAAC,CAAC,CAAC;IAEd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aACN,QAAC,CAAC,CAAC,CAAC;QACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACX,AADY,CACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAAC,CAAC,CAAC,CAAC,CACtD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gCAElB,QAAC,CAAC,CAAC,CAAC;YAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;8BAEpI,QAAC,CAAC,CAAC,CAAC;oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBACrE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAC,QAAC,CAAC,CAAC,CAAC;4BAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;sCACvE,QAAC,CAAC,CAAC,CAAC;4BAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;8CACjC,QAAC,CAAC,CAAC,CAAC,CAAC;oCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;8CAC1E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;8CAEZ,QAAC,CAAC,CAAC,CAAC;oCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4DACjE,QAAC,CAAC,CAAC,CAAC,CAAC;wCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;4CAAC;0DAErE,QAAC,CAAC,CAAC,CAAC,CAAC;gDAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;0DAC3E,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;4CACD;;;;;;;;;;;;;;;;;;;;;;;;8BAQf,QAAC,CAAC,CAAC,CAAC;oBAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;wBAC3E,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,eAAC,QAAC,CAAC,CAAC,CAAC;4BAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BAAC,CAAC,CAAC,CAAC,CAAC,CAAC;sCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;sCAC1F,QAAC,CAAC,CAAC,CAAC;4BACF,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;8CAEpH,QAAC,CAAC,CAAC,CAAC;oCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oCAAC,CAAC,CAAC,CAAC,CAAC,CAAC;8CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;8CACzE,QAAC,CAAC,CAAC,CAAC;oCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;8CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;8CAC9E,QAAC,CAAC,CAAC,CAAC;oCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oCAAC,CAAC,CAAC,CAAC,CAAC,CAAC;8CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;8CAC3E,QAAC,CAAC,CAAC,CAAC;oCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;8CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;8CAC9E,QAAC,CAAC,CAAC,CAAC;oCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wCAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oCAAC,CAAC,CAAC,CAAC,CAAC,CAAC;8CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMvF,CAAC,CAAC;;KAvGI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAyGvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC"
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "134a8302c954d1b06bde92650a54f48d5f39e2fa"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_efvd3qzmv = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_efvd3qzmv();

var _s = (cov_efvd3qzmv().s[0]++, __webpack_require__.$Refresh$.signature());




// Define variants using class-variance-authority
const flashSaleBannerVariants = (cov_efvd3qzmv().s[1]++, (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_5__.cva)('flex items-center rounded-card border gap-2 w-full', {
  variants: {
    variant: {
      default: 'border-secondary bg-secondary-bg text-black',
      primary: 'border-blue-400 bg-blue-50 text-black',
      secondary: 'border-purple-400 bg-purple-50 text-black',
      danger: 'border-red-400 bg-red-50 text-black'
    },
    size: {
      sm: 'text-sm min-h-[80px]',
      default: 'text-base sm:h-[96px] sm:w-[548px]',
      lg: 'text-lg min-h-[112px]'
    }
  },
  defaultVariants: {
    variant: 'default',
    size: 'default'
  }
}));
// Timer box
const timerBoxVariants = (cov_efvd3qzmv().s[2]++, (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_5__.cva)('font-bold px-3 py-2 text-xl rounded-sm', {
  variants: {
    variant: {
      default: 'bg-[#FFD904] text-black',
      primary: 'bg-blue-400 text-white',
      secondary: 'bg-purple-400 text-white',
      danger: 'bg-red-400 text-white'
    }
  },
  defaultVariants: {
    variant: 'default'
  }
}));
// Discount badge
const discountBadgeVariants = (cov_efvd3qzmv().s[3]++, (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_5__.cva)('px-2 py-1 rounded-full text-xs font-bold', {
  variants: {
    variant: {
      default: 'bg-red-500 text-white',
      primary: 'bg-blue-600 text-white',
      secondary: 'bg-purple-600 text-white',
      danger: 'bg-red-600 text-white'
    }
  },
  defaultVariants: {
    variant: 'default'
  }
}));
cov_efvd3qzmv().s[4]++;
const FlashSaleBanner = ({
  className,
  variant,
  size,
  endTime,
  title = (cov_efvd3qzmv().b[0][0]++, 'FLASH SALE'),
  subtitle = (cov_efvd3qzmv().b[1][0]++, 'แสงอรุณนครินทร์'),
  discount = (cov_efvd3qzmv().b[2][0]++, '-11%'),
  showDiscount = (cov_efvd3qzmv().b[3][0]++, false),
  onTimeEnd,
  icon = (/*#__PURE__*/cov_efvd3qzmv().b[4][0]++, (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__["default"], {
    src: "/images/lightning.png",
    alt: "Flash sale icon",
    width: 52,
    height: 52,
    className: "h-7 w-7 object-contain sm:h-[52px] sm:w-[52px]"
  }, void 0, false, {
    fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\banner-flash-sale.tsx",
    lineNumber: 82,
    columnNumber: 5
  }, undefined)),
  iconPosition = (cov_efvd3qzmv().b[5][0]++, 'right'),
  testId = (cov_efvd3qzmv().b[6][0]++, 'flash-sale-banner')
}) => {
  cov_efvd3qzmv().f[0]++;
  cov_efvd3qzmv().s[5]++;
  _s();
  const [timeLeft, setTimeLeft] = (cov_efvd3qzmv().s[6]++, (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(calculateTimeLeft()));
  function calculateTimeLeft() {
    cov_efvd3qzmv().f[1]++;
    const endDateTime = (cov_efvd3qzmv().s[7]++, endTime instanceof Date ? (cov_efvd3qzmv().b[7][0]++, endTime) : (cov_efvd3qzmv().b[7][1]++, new Date(endTime)));
    const difference = (cov_efvd3qzmv().s[8]++, endDateTime.getTime() - new Date().getTime());
    cov_efvd3qzmv().s[9]++;
    if (difference <= 0) {
      cov_efvd3qzmv().b[8][0]++;
      cov_efvd3qzmv().s[10]++;
      if (onTimeEnd) {
        cov_efvd3qzmv().b[9][0]++;
        cov_efvd3qzmv().s[11]++;
        onTimeEnd();
      } else {
        cov_efvd3qzmv().b[9][1]++;
      }
      cov_efvd3qzmv().s[12]++;
      return {
        hours: '00',
        minutes: '00',
        seconds: '00'
      };
    } else {
      cov_efvd3qzmv().b[8][1]++;
    }
    cov_efvd3qzmv().s[13]++;
    return {
      hours: Math.floor(difference / (1000 * 60 * 60) % 24).toString().padStart(2, '0'),
      minutes: Math.floor(difference / 1000 / 60 % 60).toString().padStart(2, '0'),
      seconds: Math.floor(difference / 1000 % 60).toString().padStart(2, '0')
    };
  }
  cov_efvd3qzmv().s[14]++;
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({
    "FlashSaleBanner.useEffect": () => {
      cov_efvd3qzmv().f[2]++;
      const timer = (cov_efvd3qzmv().s[15]++, setInterval({
        "FlashSaleBanner.useEffect.timer": () => {
          cov_efvd3qzmv().f[3]++;
          cov_efvd3qzmv().s[16]++;
          setTimeLeft(calculateTimeLeft());
        }
      }["FlashSaleBanner.useEffect.timer"], 1000));
      cov_efvd3qzmv().s[17]++;
      return {
        "FlashSaleBanner.useEffect": () => {
          cov_efvd3qzmv().f[4]++;
          cov_efvd3qzmv().s[18]++;
          return clearInterval(timer);
        }
      }["FlashSaleBanner.useEffect"];
    }
  }["FlashSaleBanner.useEffect"], [endTime]);
  cov_efvd3qzmv().s[19]++;
  return /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(flashSaleBannerVariants({
      variant,
      size,
      className
    }), 'mx-auto flex-col sm:flex-row'),
    "data-testid": testId,
    role: "alert",
    "aria-live": "polite",
    children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
      className: "flex h-full w-full flex-col items-center gap-3 px-4 text-center sm:flex-row sm:justify-between sm:px-4  sm:text-left",
      children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
        className: "flex flex-col items-center sm:flex-row sm:items-center",
        children: [(cov_efvd3qzmv().b[10][0]++, iconPosition === 'left') && (cov_efvd3qzmv().b[10][1]++, /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
          className: "text-current",
          children: icon
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\banner-flash-sale.tsx",
          lineNumber: 140,
          columnNumber: 39
        }, undefined)), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
          className: "font-bold sm:ml-2",
          children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("span", {
            className: " sm:text-body-16-none block text-title text-sm font-medium",
            children: subtitle
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\banner-flash-sale.tsx",
            lineNumber: 142,
            columnNumber: 13
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
            className: "flex items-center justify-center sm:justify-start",
            children: /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("span", {
              className: "text-text-lg-bold sm:text-text-3xl flex items-center",
              children: ["FLASH", /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("span", {
                className: "-mx-1 -mt-1 inline-flex items-start justify-center sm:-mx-2",
                children: icon
              }, void 0, false, {
                fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\banner-flash-sale.tsx",
                lineNumber: 148,
                columnNumber: 17
              }, undefined), "SALE"]
            }, void 0, true, {
              fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\banner-flash-sale.tsx",
              lineNumber: 146,
              columnNumber: 15
            }, undefined)
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\banner-flash-sale.tsx",
            lineNumber: 145,
            columnNumber: 13
          }, undefined)]
        }, void 0, true, {
          fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\banner-flash-sale.tsx",
          lineNumber: 141,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\banner-flash-sale.tsx",
        lineNumber: 139,
        columnNumber: 9
      }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
        className: "flex flex-col items-center gap-2 sm:flex-row sm:items-center",
        children: [(cov_efvd3qzmv().b[11][0]++, showDiscount) && (cov_efvd3qzmv().b[11][1]++, /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
          className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(discountBadgeVariants({
            variant
          })),
          children: discount
        }, void 0, false, {
          fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\banner-flash-sale.tsx",
          lineNumber: 159,
          columnNumber: 28
        }, undefined)), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
          className: "flex",
          "aria-label": `Time remaining: ${timeLeft.hours} hours, ${timeLeft.minutes} minutes, and ${timeLeft.seconds} seconds`,
          children: [/*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(timerBoxVariants({
              variant
            })),
            children: timeLeft.hours
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\banner-flash-sale.tsx",
            lineNumber: 164,
            columnNumber: 13
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
            className: "flex items-center px-1 text-xl font-bold text-current",
            children: ":"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\banner-flash-sale.tsx",
            lineNumber: 165,
            columnNumber: 13
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(timerBoxVariants({
              variant
            })),
            children: timeLeft.minutes
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\banner-flash-sale.tsx",
            lineNumber: 166,
            columnNumber: 13
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
            className: "flex items-center px-1 text-xl font-bold text-current",
            children: ":"
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\banner-flash-sale.tsx",
            lineNumber: 167,
            columnNumber: 13
          }, undefined), /*#__PURE__*/(0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)("div", {
            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(timerBoxVariants({
              variant
            })),
            children: timeLeft.seconds
          }, void 0, false, {
            fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\banner-flash-sale.tsx",
            lineNumber: 168,
            columnNumber: 13
          }, undefined)]
        }, void 0, true, {
          fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\banner-flash-sale.tsx",
          lineNumber: 160,
          columnNumber: 11
        }, undefined)]
      }, void 0, true, {
        fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\banner-flash-sale.tsx",
        lineNumber: 158,
        columnNumber: 9
      }, undefined)]
    }, void 0, true, {
      fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\banner-flash-sale.tsx",
      lineNumber: 137,
      columnNumber: 7
    }, undefined)
  }, void 0, false, {
    fileName: "C:\\Users\\<USER>\\Desktop\\fristJob\\sangaroon-nakharin-ecommerce\\frontend\\home\\src\\components\\Banner\\banner-flash-sale.tsx",
    lineNumber: 128,
    columnNumber: 5
  }, undefined);
};
cov_efvd3qzmv().s[20]++;
_s(FlashSaleBanner, "2yD7J9BRKO6eNclZT/dCzRz3Fpg=");
cov_efvd3qzmv().s[21]++;
_c = FlashSaleBanner;
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlashSaleBanner);
cov_efvd3qzmv().s[22]++;
FlashSaleBanner.__docgenInfo = {
  "description": "",
  "methods": [],
  "displayName": "FlashSaleBanner",
  "props": {
    "className": {
      "required": false,
      "tsType": {
        "name": "string"
      },
      "description": ""
    },
    "endTime": {
      "required": true,
      "tsType": {
        "name": "union",
        "raw": "Date | string",
        "elements": [{
          "name": "Date"
        }, {
          "name": "string"
        }]
      },
      "description": ""
    },
    "title": {
      "required": false,
      "tsType": {
        "name": "string"
      },
      "description": "",
      "defaultValue": {
        "value": "'FLASH SALE'",
        "computed": false
      }
    },
    "subtitle": {
      "required": false,
      "tsType": {
        "name": "string"
      },
      "description": "",
      "defaultValue": {
        "value": "'แสงอรุณนครินทร์'",
        "computed": false
      }
    },
    "discount": {
      "required": false,
      "tsType": {
        "name": "string"
      },
      "description": "",
      "defaultValue": {
        "value": "'-11%'",
        "computed": false
      }
    },
    "showDiscount": {
      "required": false,
      "tsType": {
        "name": "boolean"
      },
      "description": "",
      "defaultValue": {
        "value": "false",
        "computed": false
      }
    },
    "onTimeEnd": {
      "required": false,
      "tsType": {
        "name": "signature",
        "type": "function",
        "raw": "() => void",
        "signature": {
          "arguments": [],
          "return": {
            "name": "void"
          }
        }
      },
      "description": ""
    },
    "icon": {
      "required": false,
      "tsType": {
        "name": "ReactNode"
      },
      "description": "",
      "defaultValue": {
        "value": "<Image\r\n  src=\"/images/lightning.png\"\r\n  alt=\"Flash sale icon\"\r\n  width={52}\r\n  height={52}\r\n  className=\"h-7 w-7 object-contain sm:h-[52px] sm:w-[52px]\"\r\n/>",
        "computed": false
      }
    },
    "iconPosition": {
      "required": false,
      "tsType": {
        "name": "union",
        "raw": "'left' | 'right'",
        "elements": [{
          "name": "literal",
          "value": "'left'"
        }, {
          "name": "literal",
          "value": "'right'"
        }]
      },
      "description": "",
      "defaultValue": {
        "value": "'right'",
        "computed": false
      }
    },
    "testId": {
      "required": false,
      "tsType": {
        "name": "string"
      },
      "description": "",
      "defaultValue": {
        "value": "'flash-sale-banner'",
        "computed": false
      }
    }
  },
  "composes": ["VariantProps"]
};
var _c;
cov_efvd3qzmv().s[23]++;
__webpack_require__.$Refresh$.register(_c, "FlashSaleBanner");

const $ReactRefreshModuleId$ = __webpack_require__.$Refresh$.moduleId;
const $ReactRefreshCurrentExports$ = __react_refresh_utils__.getModuleExports(
	$ReactRefreshModuleId$
);

function $ReactRefreshModuleRuntime$(exports) {
	if (true) {
		let errorOverlay;
		if (true) {
			errorOverlay = false;
		}
		let testMode;
		if (typeof __react_refresh_test__ !== 'undefined') {
			testMode = __react_refresh_test__;
		}
		return __react_refresh_utils__.executeRuntime(
			exports,
			$ReactRefreshModuleId$,
			module.hot,
			errorOverlay,
			testMode
		);
	}
}

if (typeof Promise !== 'undefined' && $ReactRefreshCurrentExports$ instanceof Promise) {
	$ReactRefreshCurrentExports$.then($ReactRefreshModuleRuntime$);
} else {
	$ReactRefreshModuleRuntime$($ReactRefreshCurrentExports$);
}

/***/ })

}]);
//# sourceMappingURL=components-Banner-__stories__-banner-flash-sale-stories.iframe.bundle.js.map